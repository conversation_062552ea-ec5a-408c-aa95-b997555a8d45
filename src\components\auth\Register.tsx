
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { User, Mail, Lock, ArrowRight } from "lucide-react";
import PasswordStrength from "./PasswordStrength";

interface RegisterProps {
  onToggleMode: () => void;
}

const Register = ({ onToggleMode }: RegisterProps) => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Error",
        description: "Las contraseñas no coinciden",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // Simulate registration process
    setTimeout(() => {
      setIsLoading(false);
      toast({
        title: "Cuenta creada exitosamente",
        description: "Bienvenido, ya puedes iniciar sesión",
      });
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-5">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-green-100 font-medium">
              Nombre
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400 h-4 w-4" />
              <Input
                id="firstName"
                name="firstName"
                type="text"
                placeholder="Nombre"
                value={formData.firstName}
                onChange={handleInputChange}
                className="pl-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300 hover:bg-slate-700/50"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-green-100 font-medium">
              Apellido
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400 h-4 w-4" />
              <Input
                id="lastName"
                name="lastName"
                type="text"
                placeholder="Apellido"
                value={formData.lastName}
                onChange={handleInputChange}
                className="pl-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300 hover:bg-slate-700/50"
                required
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-green-100 font-medium">
            Correo electrónico
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400 h-4 w-4" />
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleInputChange}
              className="pl-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300 hover:bg-slate-700/50"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-green-100 font-medium">
            Contraseña
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400 h-4 w-4" />
            <Input
              id="password"
              name="password"
              type="password"
              placeholder="••••••••"
              value={formData.password}
              onChange={handleInputChange}
              className="pl-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300 hover:bg-slate-700/50"
              required
            />
          </div>
          {formData.password && (
            <div className="mt-3">
              <PasswordStrength password={formData.password} />
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-green-100 font-medium">
            Confirmar contraseña
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400 h-4 w-4" />
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              placeholder="••••••••"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className="pl-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 transition-all duration-300 hover:bg-slate-700/50"
              required
            />
          </div>
          {formData.confirmPassword && formData.password !== formData.confirmPassword && (
            <p className="text-red-400 text-sm mt-2 flex items-center space-x-1">
              <span>⚠️</span>
              <span>Las contraseñas no coinciden</span>
            </p>
          )}
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold py-3 h-12 rounded-xl transition-all duration-300 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed shadow-lg group"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>Creando cuenta...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <span>Crear cuenta</span>
              <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
            </div>
          )}
        </Button>
      </form>

      <div className="text-center lg:text-left">
        <p className="text-slate-300 text-sm">
          ¿Ya tienes una cuenta?{" "}
          <button
            onClick={onToggleMode}
            className="text-green-400 hover:text-green-300 font-semibold transition-colors hover:underline"
          >
            Inicia sesión aquí
          </button>
        </p>
      </div>
    </div>
  );
};

export default Register;
