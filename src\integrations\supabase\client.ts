// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://lkbjrwozxtpewyakjddg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxrYmpyd296eHRwZXd5YWtqZGRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5MjA2MjIsImV4cCI6MjA2NDQ5NjYyMn0.YTgYP391FrxDIdn1OiwcQlgNMHr2ATNB-TjfILkddJU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);