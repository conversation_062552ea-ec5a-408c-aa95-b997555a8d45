
import { useState } from "react";
import Login from "@/components/auth/Login";
import Register from "@/components/auth/Register";

const Index = () => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-emerald-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-green-500/10 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full bg-emerald-500/10 blur-3xl"></div>
      </div>
      
      <div className={`w-full relative z-10 ${isLogin ? 'max-w-md' : 'max-w-4xl'}`}>
        <div className="bg-slate-800/40 backdrop-blur-xl rounded-3xl shadow-2xl border border-green-700/20 p-8 animate-scale-in">
          {isLogin ? (
            <>
              {/* Character Image for Login */}
              <div className="flex justify-center mb-8 animate-fade-in">
                <div className="relative">
                  <img
                    src="/lovable-uploads/pasteur.png"
                    alt="Character illustration"
                    className="w-40 h-40 object-contain animate-float drop-shadow-2xl"
                  />
                  <div className="absolute inset-0 bg-green-400/20 rounded-full blur-xl scale-75"></div>
                </div>
              </div>

              {/* Form Container for Login */}
              <div className="animate-slide-in">
                <Login onToggleMode={() => setIsLogin(false)} />
              </div>
            </>
          ) : (
            /* Horizontal Layout for Register */
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center min-h-[600px]">
              {/* Character Image for Register */}
              <div className="flex flex-col items-center justify-center lg:justify-center animate-fade-in space-y-4">
                <div className="relative">
                  <img
                    src="/lovable-uploads/pasteur.png"
                    alt="Character illustration"
                    className="w-48 h-48 lg:w-56 lg:h-56 object-contain animate-float drop-shadow-2xl"
                  />
                  <div className="absolute inset-0 bg-green-400/20 rounded-full blur-xl scale-75"></div>
                </div>
                <div className="text-center">
                  <p className="text-green-200/80 text-sm">
                    Únete y comienza tu experiencia masiva bro
                  </p>
                </div>
              </div>

              {/* Form Container for Register */}
              <div className="animate-slide-in flex items-center">
                <div className="w-full">
                  <Register onToggleMode={() => setIsLogin(true)} />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
